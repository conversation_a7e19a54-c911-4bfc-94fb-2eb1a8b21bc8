// 此文件包含按正则表达式对文档选项卡进行着色的规则。每行都包含一个正则表达式，该表达式将针对文件的完整路径进行测试。与正则表达式匹配的所有文件都将共享一种颜色。
// 可以通过右键单击选项卡并选择“设置制表符颜色”来自定义分配给任何文件组的颜色。
// 正则表达式将按照它们在此文件中的显示顺序进行匹配。有关语法，请参阅 https://docs.microsoft.com/en-us/dotnet/standard/base-types/regular-expressions。
// 正则表达式匹配为不区分大小写。可以使用捕获组选项(如"(?-i:expression)")重写此行为。

// 编辑此文件并保存更改以立即查看应用的更改。分析或计算表达式期间遇到的任何错误都将出现在名为 按正则表达式显示颜色 的窗格的输出窗口中。
^.*\.cs$
^.*\.fs$
^.*\.vb$
^.*\.cp?p?$
^.*\.hp?p?$
