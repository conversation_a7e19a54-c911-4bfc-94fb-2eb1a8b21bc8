C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.7601.17514_none_41e6975e2bd6f2b2\comctl32.dll 6.10 (win7_rtm.090713-1255)
C:\Windows\syswow64\OLEAUT32.DLL 6.1.7601.18640
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\e3641fa3359f37ad12c84183ce765093\System.Core.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\syswow64\psapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\version.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\ntmarta.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\WLDAP32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\pcwum.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\system32\pdh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\MSVCR110_CLR0400.dll 11.00.50938.18408 built by: FX451RTMGREL
C:\Windows\system32\aspnet_counters.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.0.30319.34237 built by: FX452RTMGDR
C:\Windows\system32\MSVCR120_CLR0400.dll 12.00.51677.34237 built by: FX452RTMGDR
C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll 1.1.4322.2502
C:\Windows\syswow64\USERENV.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v1.1.4322\MSVCR71.dll 7.10.3052.4
C:\Windows\syswow64\WS2_32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\NSI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Program Files (x86)\Microsoft SQL Server\100\Shared\instapi10.dll 2007.0100.1600.022 ((SQL_PreRelease).080709-1414 )
C:\Windows\syswow64\CFGMGR32.dll 6.1.7601.17621 (win7sp1_gdr.110523-2108)
C:\Windows\system32\rasman.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\System32\perfos.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.5483 (Win7SP1GDR.050727-5400)

Application:       BotTemplate
Version:           *******
Date:              02.01.2015 19:00:39
Computer name:     ANO-PC
User name:         Ano
OS:                Microsoft Windows NT 6.1.7601 Service Pack 1
Culture:           de-DE
Resolution:        {Width=1280, Height=960}
System up time:    08:48:33.9790000
App up time:       00:00:30.8074457
Total memory:      7159Mb
Available memory:  4051Mb

Exception classes:   
   System.ArgumentException
Exception messages: 
   Es wird kein Prozess mit der ID 5936 ausgeführt.
Stack Traces:
   bei System.Diagnostics.Process.GetProcessById(Int32 processId, String machineName)
   bei System.Diagnostics.Process.GetProcessById(Int32 processId)
   bei BotTemplate.Interact.ObjectManager.IsWowCrashed() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\ObjectManager.cs:Zeile 462.
   bei BotTemplate.Forms.mainForm.button3_Click(Object sender, EventArgs e) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Forms\mainForm.cs:Zeile 828.
   bei System.Windows.Forms.Control.OnClick(EventArgs e)
   bei System.Windows.Forms.Button.OnClick(EventArgs e)
   bei System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   bei System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   bei System.Windows.Forms.Control.WndProc(Message& m)
   bei System.Windows.Forms.ButtonBase.WndProc(Message& m)
   bei System.Windows.Forms.Button.WndProc(Message& m)
   bei System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   bei System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   bei System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
Loaded Modules:
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SysWOW64\ntdll.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SYSTEM32\MSCOREE.DLL 4.0.40305.0 (Main.040305-0000)
C:\Windows\syswow64\KERNEL32.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\KERNELBASE.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\ADVAPI32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\msvcrt.dll 7.0.7601.17744 (win7sp1_gdr.111215-1535)
C:\Windows\SysWOW64\sechost.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\RPCRT4.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\SspiCli.dll 6.1.7601.18637 (win7sp1_gdr.141013-1517)
C:\Windows\syswow64\CRYPTBASE.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\syswow64\SHLWAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\GDI32.dll 6.1.7601.18577 (win7sp1_gdr.140822-1508)
C:\Windows\syswow64\USER32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\LPK.dll 6.1.7601.18177 (win7sp1_gdr.130605-1534)
C:\Windows\syswow64\USP10.dll 1.0626.7601.18454 (win7sp1_gdr.140424-1533)
C:\Windows\system32\IMM32.DLL 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\MSCTF.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.5485 (Win7SP1GDR.050727-5400)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.6229_none_d089f796442de10e\MSVCR80.dll 8.00.50727.6229
C:\Windows\syswow64\shell32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\ole32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\profapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\38bf604432e1a30c954b2ee40d6a2d1c\mscorlib.ni.dll 2.0.50727.5485 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\908ba9e296e92b4e14bdc2437edac603\System.ni.dll 2.0.50727.5485 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\836e10dfd0811b303553216f5cb092ef\System.Drawing.ni.dll 2.0.50727.5483 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\1453d9e9a4989833ef3db4b22549ba1a\System.Windows.Forms.ni.dll 2.0.50727.5483 (Win7SP1GDR.050727-5400)
C:\Windows\system32\CRYPTSP.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\rsaenh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\RpcRtRemote.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\MSVCR90.dll 9.00.30729.6161
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\msvcm90.dll 9.00.30729.6161
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_de_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\uxtheme.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_de_b77a5c561934e089\System.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.7601.18455_none_72d576ad8665e853\gdiplus.dll 6.1.7601.18455 (win7sp1_gdr.140425-1537)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.7601.17514_none_41e6975e2bd6f2b2\comctl32.dll 6.10 (win7_rtm.090713-1255)
C:\Windows\syswow64\OLEAUT32.DLL 6.1.7601.18640
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\e3641fa3359f37ad12c84183ce765093\System.Core.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\syswow64\psapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\version.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\ntmarta.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\WLDAP32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\pcwum.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\system32\pdh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\MSVCR110_CLR0400.dll 11.00.50938.18408 built by: FX451RTMGREL
C:\Windows\system32\aspnet_counters.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.0.30319.34237 built by: FX452RTMGDR
C:\Windows\system32\MSVCR120_CLR0400.dll 12.00.51677.34237 built by: FX452RTMGDR
C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll 1.1.4322.2502
C:\Windows\syswow64\USERENV.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v1.1.4322\MSVCR71.dll 7.10.3052.4
C:\Windows\syswow64\WS2_32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\NSI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Program Files (x86)\Microsoft SQL Server\100\Shared\instapi10.dll 2007.0100.1600.022 ((SQL_PreRelease).080709-1414 )
C:\Windows\syswow64\CFGMGR32.dll 6.1.7601.17621 (win7sp1_gdr.110523-2108)
C:\Windows\system32\rasman.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\System32\perfos.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.5483 (Win7SP1GDR.050727-5400)

Application:       BotTemplate
Version:           *******
Date:              17/08/2025 12:58:29
Computer name:     DESKTOP-GIJIRPV
User name:         WilliamI5
OS:                Microsoft Windows NT 6.2.9200.0
Culture:           zh-CN
Resolution:        {Width=2048, Height=1152}
System up time:    3.03:48:51.3440000
App up time:       00:00:12.8251335
Total memory:      32581Mb
Available memory:  19343Mb

Exception classes:   
   System.ComponentModel.Win32Exception
Exception messages: 
   并非所有被引用的特权或组都分配给呼叫方。
Stack Traces:
   在 System.Diagnostics.Process.SetPrivilege(String privilegeName, Int32 attrib)
   在 System.Diagnostics.Process.EnterDebugMode()
   在 Magic.BlackMagic.Open(Int32 ProcessId) 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Helper\BlackMagic\BMMain.cs:行号 144
   在 BotTemplate.Forms.attachForm.getProcesses() 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\attachForm.cs:行号 30
   在 BotTemplate.Forms.attachForm..ctor() 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\attachForm.cs:行号 61
   在 BotTemplate.Forms.mainForm.bAttach_Click(Object sender, EventArgs e) 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\mainForm.cs:行号 87
   在 System.Windows.Forms.Control.OnClick(EventArgs e)
   在 System.Windows.Forms.Button.OnClick(EventArgs e)
   在 System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   在 System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ButtonBase.WndProc(Message& m)
   在 System.Windows.Forms.Button.WndProc(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
Loaded Modules:
E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SYSTEM32\ntdll.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\MSCOREE.DLL 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\KERNEL32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\KERNELBASE.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\apphelp.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\ADVAPI32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\msvcrt.dll 7.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\sechost.dll 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\RPCRT4.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\bcrypt.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.8.9093.0 built by: NET481REL1LAST_C
C:\Windows\System32\SHLWAPI.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\kernel.appcore.dll 10.0.19041.3758 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\VERSION.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\System32\USER32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\win32u.dll 10.0.19041.4648 (WinBuild.160101.0800)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.9672_none_d08f9da24428a513\MSVCR80.dll 8.00.50727.9672
C:\Windows\System32\GDI32.dll 10.0.19041.4474 (WinBuild.160101.0800)
C:\Windows\System32\gdi32full.dll 10.0.19041.4648 (WinBuild.160101.0800)
C:\Windows\System32\msvcp_win.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\ucrtbase.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\IMM32.DLL 10.0.19041.4474 (WinBuild.160101.0800)
C:\Windows\System32\shell32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\windows.storage.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\combase.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\Wldp.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\OLEAUT32.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\SHCORE.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\profapi.dll 10.0.19041.4355 (WinBuild.160101.0800)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\7cfe3c62614d1a69ef2495aa72404b5d\mscorlib.ni.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\System32\ole32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\bcryptPrimitives.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\uxtheme.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\c2654a75a540834f1ab7009853a8dbb2\System.ni.dll 2.0.50727.9176 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\d062eff086ee14035deb895d5d25de9f\System.Drawing.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\d1da83964121e4fca89bd9836b553914\System.Windows.Forms.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.9625_none_508ef7e4bcbbe589\MSVCR90.dll 9.00.30729.9625
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.9625_none_508ef7e4bcbbe589\msvcm90.dll 9.00.30729.9625
C:\Windows\SYSTEM32\CRYPTSP.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\rsaenh.dll 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\CRYPTBASE.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_zh-CHS_b77a5c561934e089\System.resources.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Xml\685d4c09da83b715b57a95bb7b7a9d34\System.Xml.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.19041.4597_none_d954b6f7e1016a2a\gdiplus.dll 10.0.19041.4597 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\DWrite.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\MSCTF.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\TextShaping.dll 
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_a865f0c28672571c\comctl32.dll 6.10 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\textinputframework.dll 10.0.19041.4651 (WinBuild.160101.0800)
C:\Windows\System32\CoreUIComponents.dll 10.0.19041.3636
C:\Windows\System32\CoreMessaging.dll 10.0.19041.4474
C:\Windows\System32\WS2_32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\wintypes.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\ntmarta.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\clbcatq.dll 2001.12.10941.16384 (WinBuild.160101.0800)
C:\Windows\System32\SogouTSF.ime 11.0.0.4909
C:\Windows\System32\MSIMG32.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\SogouPy.ime 11.0.0.4909
C:\Windows\SYSTEM32\WINHTTP.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\OLEACC.dll 7.2.19041.3636 (WinBuild.160101.0800)
C:\Program Files (x86)\SogouInput\11.0.0.4909\Resource.dll 11.0.0.4909
C:\Program Files (x86)\SogouInput\Components\biz_center\1.0.0.2509\biz_bundle.dll 1.0.0.2509
C:\Windows\System32\PSAPI.DLL 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\WINMM.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\USERENV.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\dwmapi.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Program Files (x86)\SogouInput\Components\game_center\1.0.0.2353\game_center.dll 1.0.0.2353
C:\Program Files (x86)\SogouInput\Components\PicFace\1.1.0.1881\PicFace.dll 1.1.0.1881
C:\Windows\System32\COMDLG32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_5.82.19041.4355_none_c0dc01d438beab35\COMCTL32.dll 5.82 (WinBuild.160101.0800)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\4f28498ede5471a347337b2b07a7d579\System.Core.ni.dll 3.5.30729.9141 built by: WinRelRS6
C:\Windows\SYSTEM32\SspiCli.dll 10.0.19041.4239 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.8.9037.0 built by: NET481REL1
C:\Windows\SYSTEM32\pdh.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\ucrtbase_clr0400.dll 14.32.31326.0
C:\Windows\SYSTEM32\VCRUNTIME140_CLR0400.dll 14.32.31326.0
C:\Windows\System32\aspnet_counters.dll 4.8.9037.0 built by: NET481REL1
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.8.9261.0 built by: NET481REL1LAST_C
C:\Windows\Microsoft.NET\Framework\v2.0.50727\aspnet_perf.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\System32\BitsProxy.dll 7.8.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\cfgmgr32.dll 10.0.19041.3996 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\rasman.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\tapi32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\perfos.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_zh-CHS_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.9149 (WinRelRS6.050727-9100)

Application:       BotTemplate
Version:           *******
Date:              17/08/2025 12:58:40
Computer name:     DESKTOP-GIJIRPV
User name:         WilliamI5
OS:                Microsoft Windows NT 6.2.9200.0
Culture:           zh-CN
Resolution:        {Width=2048, Height=1152}
System up time:    3.03:48:57.3130000
App up time:       00:00:18.7892900
Total memory:      32581Mb
Available memory:  19335Mb

Exception classes:   
   System.ComponentModel.Win32Exception
Exception messages: 
   并非所有被引用的特权或组都分配给呼叫方。
Stack Traces:
   在 System.Diagnostics.Process.SetPrivilege(String privilegeName, Int32 attrib)
   在 System.Diagnostics.Process.EnterDebugMode()
   在 Magic.BlackMagic.Open(Int32 ProcessId) 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Helper\BlackMagic\BMMain.cs:行号 144
   在 BotTemplate.Forms.attachForm.getProcesses() 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\attachForm.cs:行号 30
   在 BotTemplate.Forms.attachForm..ctor() 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\attachForm.cs:行号 61
   在 BotTemplate.Forms.mainForm.bAttach_Click(Object sender, EventArgs e) 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\mainForm.cs:行号 87
   在 System.Windows.Forms.Control.OnClick(EventArgs e)
   在 System.Windows.Forms.Button.OnClick(EventArgs e)
   在 System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   在 System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ButtonBase.WndProc(Message& m)
   在 System.Windows.Forms.Button.WndProc(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
Loaded Modules:
E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SYSTEM32\ntdll.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\MSCOREE.DLL 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\KERNEL32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\KERNELBASE.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\apphelp.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\ADVAPI32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\msvcrt.dll 7.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\sechost.dll 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\RPCRT4.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\bcrypt.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.8.9093.0 built by: NET481REL1LAST_C
C:\Windows\System32\SHLWAPI.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\kernel.appcore.dll 10.0.19041.3758 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\VERSION.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\System32\USER32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\win32u.dll 10.0.19041.4648 (WinBuild.160101.0800)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.9672_none_d08f9da24428a513\MSVCR80.dll 8.00.50727.9672
C:\Windows\System32\GDI32.dll 10.0.19041.4474 (WinBuild.160101.0800)
C:\Windows\System32\gdi32full.dll 10.0.19041.4648 (WinBuild.160101.0800)
C:\Windows\System32\msvcp_win.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\ucrtbase.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\IMM32.DLL 10.0.19041.4474 (WinBuild.160101.0800)
C:\Windows\System32\shell32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\windows.storage.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\combase.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\Wldp.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\OLEAUT32.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\SHCORE.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\profapi.dll 10.0.19041.4355 (WinBuild.160101.0800)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\7cfe3c62614d1a69ef2495aa72404b5d\mscorlib.ni.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\System32\ole32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\bcryptPrimitives.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\uxtheme.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\c2654a75a540834f1ab7009853a8dbb2\System.ni.dll 2.0.50727.9176 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\d062eff086ee14035deb895d5d25de9f\System.Drawing.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\d1da83964121e4fca89bd9836b553914\System.Windows.Forms.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.9625_none_508ef7e4bcbbe589\MSVCR90.dll 9.00.30729.9625
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.9625_none_508ef7e4bcbbe589\msvcm90.dll 9.00.30729.9625
C:\Windows\SYSTEM32\CRYPTSP.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\rsaenh.dll 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\CRYPTBASE.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_zh-CHS_b77a5c561934e089\System.resources.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Xml\685d4c09da83b715b57a95bb7b7a9d34\System.Xml.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.19041.4597_none_d954b6f7e1016a2a\gdiplus.dll 10.0.19041.4597 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\DWrite.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\MSCTF.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\TextShaping.dll 
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_a865f0c28672571c\comctl32.dll 6.10 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\textinputframework.dll 10.0.19041.4651 (WinBuild.160101.0800)
C:\Windows\System32\CoreUIComponents.dll 10.0.19041.3636
C:\Windows\System32\CoreMessaging.dll 10.0.19041.4474
C:\Windows\System32\WS2_32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\wintypes.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\ntmarta.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\clbcatq.dll 2001.12.10941.16384 (WinBuild.160101.0800)
C:\Windows\System32\SogouTSF.ime 11.0.0.4909
C:\Windows\System32\MSIMG32.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\SogouPy.ime 11.0.0.4909
C:\Windows\SYSTEM32\WINHTTP.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\OLEACC.dll 7.2.19041.3636 (WinBuild.160101.0800)
C:\Program Files (x86)\SogouInput\11.0.0.4909\Resource.dll 11.0.0.4909
C:\Program Files (x86)\SogouInput\Components\biz_center\1.0.0.2509\biz_bundle.dll 1.0.0.2509
C:\Windows\System32\PSAPI.DLL 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\WINMM.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\USERENV.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\dwmapi.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Program Files (x86)\SogouInput\Components\game_center\1.0.0.2353\game_center.dll 1.0.0.2353
C:\Program Files (x86)\SogouInput\Components\PicFace\1.1.0.1881\PicFace.dll 1.1.0.1881
C:\Windows\System32\COMDLG32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_5.82.19041.4355_none_c0dc01d438beab35\COMCTL32.dll 5.82 (WinBuild.160101.0800)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\4f28498ede5471a347337b2b07a7d579\System.Core.ni.dll 3.5.30729.9141 built by: WinRelRS6
C:\Windows\SYSTEM32\SspiCli.dll 10.0.19041.4239 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.8.9037.0 built by: NET481REL1
C:\Windows\SYSTEM32\pdh.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\ucrtbase_clr0400.dll 14.32.31326.0
C:\Windows\SYSTEM32\VCRUNTIME140_CLR0400.dll 14.32.31326.0
C:\Windows\System32\aspnet_counters.dll 4.8.9037.0 built by: NET481REL1
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.8.9261.0 built by: NET481REL1LAST_C
C:\Windows\Microsoft.NET\Framework\v2.0.50727\aspnet_perf.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\System32\BitsProxy.dll 7.8.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\cfgmgr32.dll 10.0.19041.3996 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\rasman.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\tapi32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\perfos.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_zh-CHS_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.9149 (WinRelRS6.050727-9100)

Application:       BotTemplate
Version:           *******
Date:              17/08/2025 12:58:40
Computer name:     DESKTOP-GIJIRPV
User name:         WilliamI5
OS:                Microsoft Windows NT 6.2.9200.0
Culture:           zh-CN
Resolution:        {Width=2048, Height=1152}
System up time:    3.03:48:57.7810000
App up time:       00:00:19.2552898
Total memory:      32581Mb
Available memory:  19335Mb

Exception classes:   
   System.ComponentModel.Win32Exception
Exception messages: 
   并非所有被引用的特权或组都分配给呼叫方。
Stack Traces:
   在 System.Diagnostics.Process.SetPrivilege(String privilegeName, Int32 attrib)
   在 System.Diagnostics.Process.EnterDebugMode()
   在 Magic.BlackMagic.Open(Int32 ProcessId) 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Helper\BlackMagic\BMMain.cs:行号 144
   在 BotTemplate.Forms.attachForm.getProcesses() 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\attachForm.cs:行号 30
   在 BotTemplate.Forms.attachForm..ctor() 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\attachForm.cs:行号 61
   在 BotTemplate.Forms.mainForm.bAttach_Click(Object sender, EventArgs e) 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\mainForm.cs:行号 87
   在 System.Windows.Forms.Control.OnClick(EventArgs e)
   在 System.Windows.Forms.Button.OnClick(EventArgs e)
   在 System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   在 System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ButtonBase.WndProc(Message& m)
   在 System.Windows.Forms.Button.WndProc(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
Loaded Modules:
E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SYSTEM32\ntdll.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\MSCOREE.DLL 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\KERNEL32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\KERNELBASE.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\apphelp.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\ADVAPI32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\msvcrt.dll 7.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\sechost.dll 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\RPCRT4.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\bcrypt.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.8.9093.0 built by: NET481REL1LAST_C
C:\Windows\System32\SHLWAPI.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\kernel.appcore.dll 10.0.19041.3758 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\VERSION.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\System32\USER32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\win32u.dll 10.0.19041.4648 (WinBuild.160101.0800)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.9672_none_d08f9da24428a513\MSVCR80.dll 8.00.50727.9672
C:\Windows\System32\GDI32.dll 10.0.19041.4474 (WinBuild.160101.0800)
C:\Windows\System32\gdi32full.dll 10.0.19041.4648 (WinBuild.160101.0800)
C:\Windows\System32\msvcp_win.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\ucrtbase.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\IMM32.DLL 10.0.19041.4474 (WinBuild.160101.0800)
C:\Windows\System32\shell32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\windows.storage.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\combase.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\Wldp.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\OLEAUT32.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\SHCORE.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\profapi.dll 10.0.19041.4355 (WinBuild.160101.0800)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\7cfe3c62614d1a69ef2495aa72404b5d\mscorlib.ni.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\System32\ole32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\bcryptPrimitives.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\uxtheme.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\c2654a75a540834f1ab7009853a8dbb2\System.ni.dll 2.0.50727.9176 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\d062eff086ee14035deb895d5d25de9f\System.Drawing.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\d1da83964121e4fca89bd9836b553914\System.Windows.Forms.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.9625_none_508ef7e4bcbbe589\MSVCR90.dll 9.00.30729.9625
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.9625_none_508ef7e4bcbbe589\msvcm90.dll 9.00.30729.9625
C:\Windows\SYSTEM32\CRYPTSP.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\rsaenh.dll 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\CRYPTBASE.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_zh-CHS_b77a5c561934e089\System.resources.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Xml\685d4c09da83b715b57a95bb7b7a9d34\System.Xml.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.19041.4597_none_d954b6f7e1016a2a\gdiplus.dll 10.0.19041.4597 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\DWrite.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\MSCTF.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\TextShaping.dll 
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_a865f0c28672571c\comctl32.dll 6.10 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\textinputframework.dll 10.0.19041.4651 (WinBuild.160101.0800)
C:\Windows\System32\CoreUIComponents.dll 10.0.19041.3636
C:\Windows\System32\CoreMessaging.dll 10.0.19041.4474
C:\Windows\System32\WS2_32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\wintypes.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\ntmarta.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\clbcatq.dll 2001.12.10941.16384 (WinBuild.160101.0800)
C:\Windows\System32\SogouTSF.ime 11.0.0.4909
C:\Windows\System32\MSIMG32.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\SogouPy.ime 11.0.0.4909
C:\Windows\SYSTEM32\WINHTTP.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\OLEACC.dll 7.2.19041.3636 (WinBuild.160101.0800)
C:\Program Files (x86)\SogouInput\11.0.0.4909\Resource.dll 11.0.0.4909
C:\Program Files (x86)\SogouInput\Components\biz_center\1.0.0.2509\biz_bundle.dll 1.0.0.2509
C:\Windows\System32\PSAPI.DLL 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\WINMM.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\USERENV.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\dwmapi.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Program Files (x86)\SogouInput\Components\game_center\1.0.0.2353\game_center.dll 1.0.0.2353
C:\Program Files (x86)\SogouInput\Components\PicFace\1.1.0.1881\PicFace.dll 1.1.0.1881
C:\Windows\System32\COMDLG32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_5.82.19041.4355_none_c0dc01d438beab35\COMCTL32.dll 5.82 (WinBuild.160101.0800)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\4f28498ede5471a347337b2b07a7d579\System.Core.ni.dll 3.5.30729.9141 built by: WinRelRS6
C:\Windows\SYSTEM32\SspiCli.dll 10.0.19041.4239 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.8.9037.0 built by: NET481REL1
C:\Windows\SYSTEM32\pdh.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\ucrtbase_clr0400.dll 14.32.31326.0
C:\Windows\SYSTEM32\VCRUNTIME140_CLR0400.dll 14.32.31326.0
C:\Windows\System32\aspnet_counters.dll 4.8.9037.0 built by: NET481REL1
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.8.9261.0 built by: NET481REL1LAST_C
C:\Windows\Microsoft.NET\Framework\v2.0.50727\aspnet_perf.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\System32\BitsProxy.dll 7.8.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\cfgmgr32.dll 10.0.19041.3996 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\rasman.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\tapi32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\perfos.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_zh-CHS_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.9149 (WinRelRS6.050727-9100)

Application:       BotTemplate
Version:           *******
Date:              17/08/2025 12:58:40
Computer name:     DESKTOP-GIJIRPV
User name:         WilliamI5
OS:                Microsoft Windows NT 6.2.9200.0
Culture:           zh-CN
Resolution:        {Width=2048, Height=1152}
System up time:    3.03:48:58
App up time:       00:00:19.4727782
Total memory:      32581Mb
Available memory:  19337Mb

Exception classes:   
   System.ComponentModel.Win32Exception
Exception messages: 
   并非所有被引用的特权或组都分配给呼叫方。
Stack Traces:
   在 System.Diagnostics.Process.SetPrivilege(String privilegeName, Int32 attrib)
   在 System.Diagnostics.Process.EnterDebugMode()
   在 Magic.BlackMagic.Open(Int32 ProcessId) 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Helper\BlackMagic\BMMain.cs:行号 144
   在 BotTemplate.Forms.attachForm.getProcesses() 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\attachForm.cs:行号 30
   在 BotTemplate.Forms.attachForm..ctor() 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\attachForm.cs:行号 61
   在 BotTemplate.Forms.mainForm.bAttach_Click(Object sender, EventArgs e) 位置 E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\Forms\mainForm.cs:行号 87
   在 System.Windows.Forms.Control.OnClick(EventArgs e)
   在 System.Windows.Forms.Button.OnClick(EventArgs e)
   在 System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   在 System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ButtonBase.WndProc(Message& m)
   在 System.Windows.Forms.Button.WndProc(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
Loaded Modules:
E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SYSTEM32\ntdll.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\MSCOREE.DLL 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\KERNEL32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\KERNELBASE.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\apphelp.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\ADVAPI32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\msvcrt.dll 7.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\sechost.dll 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\RPCRT4.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\bcrypt.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.8.9093.0 built by: NET481REL1LAST_C
C:\Windows\System32\SHLWAPI.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\kernel.appcore.dll 10.0.19041.3758 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\VERSION.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\System32\USER32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\win32u.dll 10.0.19041.4648 (WinBuild.160101.0800)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.9672_none_d08f9da24428a513\MSVCR80.dll 8.00.50727.9672
C:\Windows\System32\GDI32.dll 10.0.19041.4474 (WinBuild.160101.0800)
C:\Windows\System32\gdi32full.dll 10.0.19041.4648 (WinBuild.160101.0800)
C:\Windows\System32\msvcp_win.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\ucrtbase.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\IMM32.DLL 10.0.19041.4474 (WinBuild.160101.0800)
C:\Windows\System32\shell32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\windows.storage.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\combase.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\Wldp.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\OLEAUT32.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\System32\SHCORE.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\profapi.dll 10.0.19041.4355 (WinBuild.160101.0800)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\7cfe3c62614d1a69ef2495aa72404b5d\mscorlib.ni.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\System32\ole32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\bcryptPrimitives.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\uxtheme.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.9177 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\c2654a75a540834f1ab7009853a8dbb2\System.ni.dll 2.0.50727.9176 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\d062eff086ee14035deb895d5d25de9f\System.Drawing.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\d1da83964121e4fca89bd9836b553914\System.Windows.Forms.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
E:\魔兽脚本机器人\CorthezzWoWBot\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.9625_none_508ef7e4bcbbe589\MSVCR90.dll 9.00.30729.9625
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.9625_none_508ef7e4bcbbe589\msvcm90.dll 9.00.30729.9625
C:\Windows\SYSTEM32\CRYPTSP.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\rsaenh.dll 10.0.19041.1 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\CRYPTBASE.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_zh-CHS_b77a5c561934e089\System.resources.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Xml\685d4c09da83b715b57a95bb7b7a9d34\System.Xml.ni.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.19041.4597_none_d954b6f7e1016a2a\gdiplus.dll 10.0.19041.4597 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\DWrite.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\MSCTF.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\TextShaping.dll 
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_a865f0c28672571c\comctl32.dll 6.10 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\textinputframework.dll 10.0.19041.4651 (WinBuild.160101.0800)
C:\Windows\System32\CoreUIComponents.dll 10.0.19041.3636
C:\Windows\System32\CoreMessaging.dll 10.0.19041.4474
C:\Windows\System32\WS2_32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\wintypes.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\ntmarta.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\clbcatq.dll 2001.12.10941.16384 (WinBuild.160101.0800)
C:\Windows\System32\SogouTSF.ime 11.0.0.4909
C:\Windows\System32\MSIMG32.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\system32\SogouPy.ime 11.0.0.4909
C:\Windows\SYSTEM32\WINHTTP.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\OLEACC.dll 7.2.19041.3636 (WinBuild.160101.0800)
C:\Program Files (x86)\SogouInput\11.0.0.4909\Resource.dll 11.0.0.4909
C:\Program Files (x86)\SogouInput\Components\biz_center\1.0.0.2509\biz_bundle.dll 1.0.0.2509
C:\Windows\System32\PSAPI.DLL 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\WINMM.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\USERENV.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\dwmapi.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Program Files (x86)\SogouInput\Components\game_center\1.0.0.2353\game_center.dll 1.0.0.2353
C:\Program Files (x86)\SogouInput\Components\PicFace\1.1.0.1881\PicFace.dll 1.1.0.1881
C:\Windows\System32\COMDLG32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_5.82.19041.4355_none_c0dc01d438beab35\COMCTL32.dll 5.82 (WinBuild.160101.0800)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\4f28498ede5471a347337b2b07a7d579\System.Core.ni.dll 3.5.30729.9141 built by: WinRelRS6
C:\Windows\SYSTEM32\SspiCli.dll 10.0.19041.4239 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.8.9037.0 built by: NET481REL1
C:\Windows\SYSTEM32\pdh.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\ucrtbase_clr0400.dll 14.32.31326.0
C:\Windows\SYSTEM32\VCRUNTIME140_CLR0400.dll 14.32.31326.0
C:\Windows\System32\aspnet_counters.dll 4.8.9037.0 built by: NET481REL1
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.8.9261.0 built by: NET481REL1LAST_C
C:\Windows\Microsoft.NET\Framework\v2.0.50727\aspnet_perf.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\System32\BitsProxy.dll 7.8.19041.1 (WinBuild.160101.0800)
C:\Windows\System32\cfgmgr32.dll 10.0.19041.3996 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\rasman.dll 10.0.19041.3636 (WinBuild.160101.0800)
C:\Windows\SYSTEM32\tapi32.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\System32\perfos.dll 10.0.19041.4406 (WinBuild.160101.0800)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.9149 (WinRelRS6.050727-9100)
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_zh-CHS_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.9149 (WinRelRS6.050727-9100)
