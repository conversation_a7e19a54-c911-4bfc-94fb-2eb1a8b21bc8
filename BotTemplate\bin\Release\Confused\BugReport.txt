   bei System.Threading.ThreadHelper.ThreadStart()
Loaded Modules:
C:\Users\<USER>\Documents\Visual Studio 2010\Projects\BotTemplate\BotTemplate\bin\Release\BotTemplate.vshost.exe 12.0.21005.1
C:\Windows\SysWOW64\ntdll.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SYSTEM32\MSCOREE.DLL 4.0.40305.0 (Main.040305-0000)
C:\Windows\syswow64\KERNEL32.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\KERNELBASE.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\ADVAPI32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\msvcrt.dll 7.0.7601.17744 (win7sp1_gdr.111215-1535)
C:\Windows\SysWOW64\sechost.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\RPCRT4.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\SspiCli.dll 6.1.7601.18270 (win7sp1_gdr.130924-1532)
C:\Windows\syswow64\CRYPTBASE.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\syswow64\SHLWAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\GDI32.dll 6.1.7601.18275 (win7sp1_gdr.131002-1533)
C:\Windows\syswow64\USER32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\LPK.dll 6.1.7601.18177 (win7sp1_gdr.130605-1534)
C:\Windows\syswow64\USP10.dll 1.0626.7601.18009 (win7sp1_gdr.121121-1431)
C:\Windows\system32\IMM32.DLL 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\MSCTF.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.6229_none_d089f796442de10e\MSVCR80.dll 8.00.50727.6229
C:\Windows\syswow64\shell32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\ole32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\profapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\ede2c6c842840e009f01bcc74fa4c457\mscorlib.ni.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorsec.dll 2.0.50727.4927 (NetFXspW7.050727-4900)
C:\Windows\syswow64\WINTRUST.dll 6.1.7601.18205 (win7sp1_gdr.130708-1532)
C:\Windows\syswow64\CRYPT32.dll 6.1.7601.18277 (win7sp1_gdr.131005-0934)
C:\Windows\syswow64\MSASN1.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_5.82.7601.18201_none_ec80f00e8593ece5\COMCTL32.dll 5.82 (win7_rtm.090713-1255)
C:\Windows\system32\CRYPTSP.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\rsaenh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\imagehlp.dll 6.1.7601.18288 (win7sp1_gdr.131018-1533)
C:\Windows\system32\ncrypt.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\bcrypt.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SysWOW64\bcryptprimitives.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\USERENV.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\GPAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\cryptnet.dll 6.1.7601.18205 (win7sp1_gdr.130708-1532)
C:\Windows\syswow64\WLDAP32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\SensApi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\Microsoft.VisualStudio.HostingProcess.Utilities\12.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.HostingProcess.Utilities.dll 12.0.21005.1
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\syswow64\CLBCatQ.DLL 2001.12.8530.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\OLEAUT32.dll 6.1.7601.17676
C:\Windows\system32\RpcRtRemote.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\b3a78269847005365001c33870cd121f\System.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\5c24d3b0041ebf4f48a93615b9fa3de9\System.Drawing.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\8bc548587e91ecf0552a40e47bbf99cc\System.Windows.Forms.ni.dll 2.0.50727.5468 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\GAC_MSIL\Microsoft.VisualStudio.HostingProcess.Utilities.Sync\12.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.HostingProcess.Utilities.Sync.dll 12.0.21005.1
C:\Windows\system32\VERSION.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\Microsoft.VisualStudio.Debugger.Runtime\12.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Debugger.Runtime.dll 12.0.21005.1
C:\Program Files (x86)\Common Files\Microsoft Shared\VS7Debug\12.0\Microsoft.VisualStudio.Debugger.Runtime.Impl.dll 12.0.21005.1 built by: REL
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_de_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\SXS.DLL 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\psapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\868ad9d8acc0bf80a973c0e4e9cae4fa\System.Core.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Xml.Linq\801b632b8b7ef72f14333dbce41524b8\System.Xml.Linq.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Data.DataSet#\e383182777b770f5eb30064b782bff53\System.Data.DataSetExtensions.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Data\fe1942c05eda4f9744f80afb4ae76a2d\System.Data.ni.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\assembly\GAC_32\System.Data\2.0.0.0__b77a5c561934e089\System.Data.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\syswow64\WS2_32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\NSI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Deployment\7c233151b685c540524f87931632423a\System.Deployment.ni.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Xml\217ece46920546d718414291d463bb1c\System.Xml.ni.dll 2.0.50727.5476 (Win7SP1GDR.050727-5400)
C:\Users\<USER>\Documents\Visual Studio 2010\Projects\BotTemplate\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\MSVCR90.dll 9.00.30729.6161
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\msvcm90.dll 9.00.30729.6161
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\uxtheme.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_de_b77a5c561934e089\System.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.7601.18120_none_72d2e82386681b36\gdiplus.dll 6.1.7601.18120 (win7sp1_gdr.130402-1532)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.7601.17514_none_41e6975e2bd6f2b2\comctl32.dll 6.10 (win7_rtm.090713-1255)
C:\Windows\system32\ntmarta.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\pcwum.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Configuration\73ce00cfab52d23ca89457490fd5ef9a\System.Configuration.ni.dll 2.0.50727.5476 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\system32\pdh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\MSVCR110_CLR0400.dll 11.00.50938.18408 built by: FX451RTMGREL
C:\Windows\system32\aspnet_counters.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll 1.1.4322.2502
C:\Windows\Microsoft.NET\Framework\v1.1.4322\MSVCR71.dll 7.10.3052.4
C:\Program Files (x86)\Microsoft SQL Server\100\Shared\instapi10.dll 2007.0100.1600.022 ((SQL_PreRelease).080709-1414 )
C:\Windows\syswow64\CFGMGR32.dll 6.1.7601.17621 (win7sp1_gdr.110523-2108)
C:\Windows\assembly\GAC_MSIL\System.Windows.Forms.resources\2.0.0.0_de_b77a5c561934e089\System.Windows.Forms.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\rasman.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\System32\perfos.dll 6.1.7600.16385 (win7_rtm.090713-1255)

Application:       BotTemplate
Version:           *******
Date:              15.04.2014 16:04:29
Computer name:     ANO-PC
User name:         Ano
OS:                Microsoft Windows NT 6.1.7601 Service Pack 1
Culture:           de-DE
Resolution:        {Width=1280, Height=960}
System up time:    04:58:53.4470000
App up time:       00:46:10.6714733
Total memory:      7159Mb
Available memory:  3608Mb

Exception classes:   
   System.NullReferenceException
Exception messages: 
   Der Objektverweis wurde nicht auf eine Objektinstanz festgelegt.
Stack Traces:
   bei BotTemplate.Forms.teleportForm.Run() in c:\Users\<USER>\Documents\Visual Studio 2010\Projects\BotTemplate\BotTemplate\Forms\teleportForm.cs:Zeile 61.
   bei System.Threading.ThreadHelper.ThreadStart_Context(Object state)
   bei System.Threading.ExecutionContext.Run(ExecutionContext executionContext, ContextCallback callback, Object state)
   bei System.Threading.ThreadHelper.ThreadStart()
Loaded Modules:
C:\Users\<USER>\Documents\Visual Studio 2010\Projects\BotTemplate\BotTemplate\bin\Release\BotTemplate.vshost.exe 12.0.21005.1
C:\Windows\SysWOW64\ntdll.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SYSTEM32\MSCOREE.DLL 4.0.40305.0 (Main.040305-0000)
C:\Windows\syswow64\KERNEL32.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\KERNELBASE.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\ADVAPI32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\msvcrt.dll 7.0.7601.17744 (win7sp1_gdr.111215-1535)
C:\Windows\SysWOW64\sechost.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\RPCRT4.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\SspiCli.dll 6.1.7601.18270 (win7sp1_gdr.130924-1532)
C:\Windows\syswow64\CRYPTBASE.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\syswow64\SHLWAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\GDI32.dll 6.1.7601.18275 (win7sp1_gdr.131002-1533)
C:\Windows\syswow64\USER32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\LPK.dll 6.1.7601.18177 (win7sp1_gdr.130605-1534)
C:\Windows\syswow64\USP10.dll 1.0626.7601.18009 (win7sp1_gdr.121121-1431)
C:\Windows\system32\IMM32.DLL 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\MSCTF.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.6229_none_d089f796442de10e\MSVCR80.dll 8.00.50727.6229
C:\Windows\syswow64\shell32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\ole32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\profapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\ede2c6c842840e009f01bcc74fa4c457\mscorlib.ni.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorsec.dll 2.0.50727.4927 (NetFXspW7.050727-4900)
C:\Windows\syswow64\WINTRUST.dll 6.1.7601.18205 (win7sp1_gdr.130708-1532)
C:\Windows\syswow64\CRYPT32.dll 6.1.7601.18277 (win7sp1_gdr.131005-0934)
C:\Windows\syswow64\MSASN1.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_5.82.7601.18201_none_ec80f00e8593ece5\COMCTL32.dll 5.82 (win7_rtm.090713-1255)
C:\Windows\system32\CRYPTSP.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\rsaenh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\imagehlp.dll 6.1.7601.18288 (win7sp1_gdr.131018-1533)
C:\Windows\system32\ncrypt.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\bcrypt.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SysWOW64\bcryptprimitives.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\USERENV.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\GPAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\cryptnet.dll 6.1.7601.18205 (win7sp1_gdr.130708-1532)
C:\Windows\syswow64\WLDAP32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\SensApi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\Microsoft.VisualStudio.HostingProcess.Utilities\12.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.HostingProcess.Utilities.dll 12.0.21005.1
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\syswow64\CLBCatQ.DLL 2001.12.8530.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\OLEAUT32.dll 6.1.7601.17676
C:\Windows\system32\RpcRtRemote.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\b3a78269847005365001c33870cd121f\System.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\5c24d3b0041ebf4f48a93615b9fa3de9\System.Drawing.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\8bc548587e91ecf0552a40e47bbf99cc\System.Windows.Forms.ni.dll 2.0.50727.5468 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\GAC_MSIL\Microsoft.VisualStudio.HostingProcess.Utilities.Sync\12.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.HostingProcess.Utilities.Sync.dll 12.0.21005.1
C:\Windows\system32\VERSION.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\Microsoft.VisualStudio.Debugger.Runtime\12.0.0.0__b03f5f7f11d50a3a\Microsoft.VisualStudio.Debugger.Runtime.dll 12.0.21005.1
C:\Program Files (x86)\Common Files\Microsoft Shared\VS7Debug\12.0\Microsoft.VisualStudio.Debugger.Runtime.Impl.dll 12.0.21005.1 built by: REL
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_de_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\SXS.DLL 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\psapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\868ad9d8acc0bf80a973c0e4e9cae4fa\System.Core.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Xml.Linq\801b632b8b7ef72f14333dbce41524b8\System.Xml.Linq.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Data.DataSet#\e383182777b770f5eb30064b782bff53\System.Data.DataSetExtensions.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Data\fe1942c05eda4f9744f80afb4ae76a2d\System.Data.ni.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\assembly\GAC_32\System.Data\2.0.0.0__b77a5c561934e089\System.Data.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\syswow64\WS2_32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\NSI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Deployment\7c233151b685c540524f87931632423a\System.Deployment.ni.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Xml\217ece46920546d718414291d463bb1c\System.Xml.ni.dll 2.0.50727.5476 (Win7SP1GDR.050727-5400)
C:\Users\<USER>\Documents\Visual Studio 2010\Projects\BotTemplate\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\MSVCR90.dll 9.00.30729.6161
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\msvcm90.dll 9.00.30729.6161
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\uxtheme.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_de_b77a5c561934e089\System.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.7601.18120_none_72d2e82386681b36\gdiplus.dll 6.1.7601.18120 (win7sp1_gdr.130402-1532)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.7601.17514_none_41e6975e2bd6f2b2\comctl32.dll 6.10 (win7_rtm.090713-1255)
C:\Windows\system32\ntmarta.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\pcwum.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Configuration\73ce00cfab52d23ca89457490fd5ef9a\System.Configuration.ni.dll 2.0.50727.5476 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\system32\pdh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\MSVCR110_CLR0400.dll 11.00.50938.18408 built by: FX451RTMGREL
C:\Windows\system32\aspnet_counters.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll 1.1.4322.2502
C:\Windows\Microsoft.NET\Framework\v1.1.4322\MSVCR71.dll 7.10.3052.4
C:\Program Files (x86)\Microsoft SQL Server\100\Shared\instapi10.dll 2007.0100.1600.022 ((SQL_PreRelease).080709-1414 )
C:\Windows\syswow64\CFGMGR32.dll 6.1.7601.17621 (win7sp1_gdr.110523-2108)
C:\Windows\assembly\GAC_MSIL\System.Windows.Forms.resources\2.0.0.0_de_b77a5c561934e089\System.Windows.Forms.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\rasman.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\System32\perfos.dll 6.1.7600.16385 (win7_rtm.090713-1255)

Application:       BotTemplate
Version:           *******
Date:              17.04.2014 14:10:05
Computer name:     ANO-PC
User name:         Ano
OS:                Microsoft Windows NT 6.1.7601 Service Pack 1
Culture:           de-DE
Resolution:        {Width=1280, Height=960}
System up time:    03:36:56.6600000
App up time:       00:00:04.1182356
Total memory:      7159Mb
Available memory:  3323Mb

Exception classes:   
   System.Exception
Exception messages: 
   Assembly failed!  Error code: -131;  Error Line: 11
Stack Traces:
   bei Fasm.ManagedFasm.Inject(IntPtr hProcess, UInt32 dwAddress) in c:\documents and settings\brett\my documents\visual studio 2008\projects\fasmdll_managed\fasmdll_managed\fasmdll_managed.cpp:Zeile 167.
   bei Fasm.ManagedFasm.Inject(UInt32 dwAddress) in c:\documents and settings\brett\my documents\visual studio 2008\projects\fasmdll_managed\fasmdll_managed\fasmdll_managed.cpp:Zeile 174.
   bei BotTemplate.Interact.Inject.inject(String[] asm, UInt32 Addr) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 117.
   bei BotTemplate.Interact.Inject.CreatePerformDefaultActionDetour() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 445.
   bei BotTemplate.Interact.Inject.Apply() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 158.
   bei BotTemplate.Forms.mainForm.bAttach_Click(Object sender, EventArgs e) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Forms\mainForm.cs:Zeile 92.
   bei System.Windows.Forms.Control.OnClick(EventArgs e)
   bei System.Windows.Forms.Button.OnClick(EventArgs e)
   bei System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   bei System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   bei System.Windows.Forms.Control.WndProc(Message& m)
   bei System.Windows.Forms.ButtonBase.WndProc(Message& m)
   bei System.Windows.Forms.Button.WndProc(Message& m)
   bei System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   bei System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   bei System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
Loaded Modules:
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SysWOW64\ntdll.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SYSTEM32\MSCOREE.DLL 4.0.40305.0 (Main.040305-0000)
C:\Windows\syswow64\KERNEL32.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\KERNELBASE.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\ADVAPI32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\msvcrt.dll 7.0.7601.17744 (win7sp1_gdr.111215-1535)
C:\Windows\SysWOW64\sechost.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\RPCRT4.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\SspiCli.dll 6.1.7601.18270 (win7sp1_gdr.130924-1532)
C:\Windows\syswow64\CRYPTBASE.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\syswow64\SHLWAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\GDI32.dll 6.1.7601.18275 (win7sp1_gdr.131002-1533)
C:\Windows\syswow64\USER32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\LPK.dll 6.1.7601.18177 (win7sp1_gdr.130605-1534)
C:\Windows\syswow64\USP10.dll 1.0626.7601.18009 (win7sp1_gdr.121121-1431)
C:\Windows\system32\IMM32.DLL 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\MSCTF.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.6229_none_d089f796442de10e\MSVCR80.dll 8.00.50727.6229
C:\Windows\syswow64\shell32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\ole32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\profapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\ede2c6c842840e009f01bcc74fa4c457\mscorlib.ni.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\b3a78269847005365001c33870cd121f\System.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\5c24d3b0041ebf4f48a93615b9fa3de9\System.Drawing.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\8bc548587e91ecf0552a40e47bbf99cc\System.Windows.Forms.ni.dll 2.0.50727.5468 (Win7SP1GDR.050727-5400)
C:\Windows\system32\CRYPTSP.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\rsaenh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\RpcRtRemote.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\CLBCatQ.DLL 2001.12.8530.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\OLEAUT32.dll 6.1.7601.17676
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\MSVCR90.dll 9.00.30729.6161
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\msvcm90.dll 9.00.30729.6161
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_de_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\uxtheme.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_de_b77a5c561934e089\System.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.7601.18120_none_72d2e82386681b36\gdiplus.dll 6.1.7601.18120 (win7sp1_gdr.130402-1532)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.7601.17514_none_41e6975e2bd6f2b2\comctl32.dll 6.10 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\868ad9d8acc0bf80a973c0e4e9cae4fa\System.Core.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\syswow64\psapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\version.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\ntmarta.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\WLDAP32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\pcwum.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\system32\pdh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\MSVCR110_CLR0400.dll 11.00.50938.18408 built by: FX451RTMGREL
C:\Windows\system32\aspnet_counters.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll 1.1.4322.2502
C:\Windows\system32\USERENV.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v1.1.4322\MSVCR71.dll 7.10.3052.4
C:\Windows\syswow64\WS2_32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\NSI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Program Files (x86)\Microsoft SQL Server\100\Shared\instapi10.dll 2007.0100.1600.022 ((SQL_PreRelease).080709-1414 )
C:\Windows\syswow64\CFGMGR32.dll 6.1.7601.17621 (win7sp1_gdr.110523-2108)
C:\Windows\system32\rasman.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\System32\perfos.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.5420 (Win7SP1.050727-5400)

Application:       BotTemplate
Version:           *******
Date:              17.04.2014 14:11:11
Computer name:     ANO-PC
User name:         Ano
OS:                Microsoft Windows NT 6.1.7601 Service Pack 1
Culture:           de-DE
Resolution:        {Width=1280, Height=960}
System up time:    03:38:01.9150000
App up time:       00:00:03.6412083
Total memory:      7159Mb
Available memory:  3304Mb

Exception classes:   
   System.Exception
Exception messages: 
   Assembly failed!  Error code: -131;  Error Line: 11
Stack Traces:
   bei Fasm.ManagedFasm.Inject(IntPtr hProcess, UInt32 dwAddress) in c:\documents and settings\brett\my documents\visual studio 2008\projects\fasmdll_managed\fasmdll_managed\fasmdll_managed.cpp:Zeile 167.
   bei Fasm.ManagedFasm.Inject(UInt32 dwAddress) in c:\documents and settings\brett\my documents\visual studio 2008\projects\fasmdll_managed\fasmdll_managed\fasmdll_managed.cpp:Zeile 174.
   bei BotTemplate.Interact.Inject.inject(String[] asm, UInt32 Addr) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 117.
   bei BotTemplate.Interact.Inject.CreatePerformDefaultActionDetour() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 446.
   bei BotTemplate.Interact.Inject.Apply() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 158.
   bei BotTemplate.Forms.mainForm.bAttach_Click(Object sender, EventArgs e) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Forms\mainForm.cs:Zeile 92.
   bei System.Windows.Forms.Control.OnClick(EventArgs e)
   bei System.Windows.Forms.Button.OnClick(EventArgs e)
   bei System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   bei System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   bei System.Windows.Forms.Control.WndProc(Message& m)
   bei System.Windows.Forms.ButtonBase.WndProc(Message& m)
   bei System.Windows.Forms.Button.WndProc(Message& m)
   bei System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   bei System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   bei System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
Loaded Modules:
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SysWOW64\ntdll.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SYSTEM32\MSCOREE.DLL 4.0.40305.0 (Main.040305-0000)
C:\Windows\syswow64\KERNEL32.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\KERNELBASE.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\ADVAPI32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\msvcrt.dll 7.0.7601.17744 (win7sp1_gdr.111215-1535)
C:\Windows\SysWOW64\sechost.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\RPCRT4.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\SspiCli.dll 6.1.7601.18270 (win7sp1_gdr.130924-1532)
C:\Windows\syswow64\CRYPTBASE.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\syswow64\SHLWAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\GDI32.dll 6.1.7601.18275 (win7sp1_gdr.131002-1533)
C:\Windows\syswow64\USER32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\LPK.dll 6.1.7601.18177 (win7sp1_gdr.130605-1534)
C:\Windows\syswow64\USP10.dll 1.0626.7601.18009 (win7sp1_gdr.121121-1431)
C:\Windows\system32\IMM32.DLL 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\MSCTF.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.6229_none_d089f796442de10e\MSVCR80.dll 8.00.50727.6229
C:\Windows\syswow64\shell32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\ole32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\profapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\ede2c6c842840e009f01bcc74fa4c457\mscorlib.ni.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\b3a78269847005365001c33870cd121f\System.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\5c24d3b0041ebf4f48a93615b9fa3de9\System.Drawing.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\8bc548587e91ecf0552a40e47bbf99cc\System.Windows.Forms.ni.dll 2.0.50727.5468 (Win7SP1GDR.050727-5400)
C:\Windows\system32\CRYPTSP.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\rsaenh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\RpcRtRemote.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\CLBCatQ.DLL 2001.12.8530.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\OLEAUT32.dll 6.1.7601.17676
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\MSVCR90.dll 9.00.30729.6161
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\msvcm90.dll 9.00.30729.6161
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_de_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\uxtheme.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_de_b77a5c561934e089\System.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.7601.18120_none_72d2e82386681b36\gdiplus.dll 6.1.7601.18120 (win7sp1_gdr.130402-1532)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.7601.17514_none_41e6975e2bd6f2b2\comctl32.dll 6.10 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\868ad9d8acc0bf80a973c0e4e9cae4fa\System.Core.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\syswow64\psapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\version.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\ntmarta.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\WLDAP32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\pcwum.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\system32\pdh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\MSVCR110_CLR0400.dll 11.00.50938.18408 built by: FX451RTMGREL
C:\Windows\system32\aspnet_counters.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll 1.1.4322.2502
C:\Windows\system32\USERENV.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v1.1.4322\MSVCR71.dll 7.10.3052.4
C:\Windows\syswow64\WS2_32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\NSI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Program Files (x86)\Microsoft SQL Server\100\Shared\instapi10.dll 2007.0100.1600.022 ((SQL_PreRelease).080709-1414 )
C:\Windows\syswow64\CFGMGR32.dll 6.1.7601.17621 (win7sp1_gdr.110523-2108)
C:\Windows\system32\rasman.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\System32\perfos.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.5420 (Win7SP1.050727-5400)

Application:       BotTemplate
Version:           *******
Date:              17.04.2014 14:23:17
Computer name:     ANO-PC
User name:         Ano
OS:                Microsoft Windows NT 6.1.7601 Service Pack 1
Culture:           de-DE
Resolution:        {Width=1280, Height=960}
System up time:    03:50:07.7860000
App up time:       00:00:08.5214874
Total memory:      7159Mb
Available memory:  3274Mb

Exception classes:   
   System.Exception
Exception messages: 
   Assembly failed!  Error code: -111;  Error Line: 22
Stack Traces:
   bei Fasm.ManagedFasm.Inject(IntPtr hProcess, UInt32 dwAddress) in c:\documents and settings\brett\my documents\visual studio 2008\projects\fasmdll_managed\fasmdll_managed\fasmdll_managed.cpp:Zeile 167.
   bei Fasm.ManagedFasm.Inject(UInt32 dwAddress) in c:\documents and settings\brett\my documents\visual studio 2008\projects\fasmdll_managed\fasmdll_managed\fasmdll_managed.cpp:Zeile 174.
   bei BotTemplate.Interact.Inject.inject(String[] asm, UInt32 Addr) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 117.
   bei BotTemplate.Interact.Inject.CreatePerformDefaultActionDetour() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 458.
   bei BotTemplate.Interact.Inject.Apply() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Inject.cs:Zeile 158.
   bei BotTemplate.Forms.mainForm.bAttach_Click(Object sender, EventArgs e) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Forms\mainForm.cs:Zeile 92.
   bei System.Windows.Forms.Control.OnClick(EventArgs e)
   bei System.Windows.Forms.Button.OnClick(EventArgs e)
   bei System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   bei System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   bei System.Windows.Forms.Control.WndProc(Message& m)
   bei System.Windows.Forms.ButtonBase.WndProc(Message& m)
   bei System.Windows.Forms.Button.WndProc(Message& m)
   bei System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   bei System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   bei System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
Loaded Modules:
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SysWOW64\ntdll.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SYSTEM32\MSCOREE.DLL 4.0.40305.0 (Main.040305-0000)
C:\Windows\syswow64\KERNEL32.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\KERNELBASE.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\ADVAPI32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\msvcrt.dll 7.0.7601.17744 (win7sp1_gdr.111215-1535)
C:\Windows\SysWOW64\sechost.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\RPCRT4.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\SspiCli.dll 6.1.7601.18270 (win7sp1_gdr.130924-1532)
C:\Windows\syswow64\CRYPTBASE.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\syswow64\SHLWAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\GDI32.dll 6.1.7601.18275 (win7sp1_gdr.131002-1533)
C:\Windows\syswow64\USER32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\LPK.dll 6.1.7601.18177 (win7sp1_gdr.130605-1534)
C:\Windows\syswow64\USP10.dll 1.0626.7601.18009 (win7sp1_gdr.121121-1431)
C:\Windows\system32\IMM32.DLL 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\MSCTF.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.6229_none_d089f796442de10e\MSVCR80.dll 8.00.50727.6229
C:\Windows\syswow64\shell32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\ole32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\profapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\ede2c6c842840e009f01bcc74fa4c457\mscorlib.ni.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\b3a78269847005365001c33870cd121f\System.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\5c24d3b0041ebf4f48a93615b9fa3de9\System.Drawing.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\8bc548587e91ecf0552a40e47bbf99cc\System.Windows.Forms.ni.dll 2.0.50727.5468 (Win7SP1GDR.050727-5400)
C:\Windows\system32\CRYPTSP.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\rsaenh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\RpcRtRemote.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\CLBCatQ.DLL 2001.12.8530.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\OLEAUT32.dll 6.1.7601.17676
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\MSVCR90.dll 9.00.30729.6161
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\msvcm90.dll 9.00.30729.6161
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_de_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\uxtheme.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_de_b77a5c561934e089\System.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.7601.18120_none_72d2e82386681b36\gdiplus.dll 6.1.7601.18120 (win7sp1_gdr.130402-1532)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.7601.17514_none_41e6975e2bd6f2b2\comctl32.dll 6.10 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\868ad9d8acc0bf80a973c0e4e9cae4fa\System.Core.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\syswow64\psapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\version.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\ntmarta.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\WLDAP32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\pcwum.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\system32\pdh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\MSVCR110_CLR0400.dll 11.00.50938.18408 built by: FX451RTMGREL
C:\Windows\system32\aspnet_counters.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll 1.1.4322.2502
C:\Windows\system32\USERENV.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v1.1.4322\MSVCR71.dll 7.10.3052.4
C:\Windows\syswow64\WS2_32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\NSI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Program Files (x86)\Microsoft SQL Server\100\Shared\instapi10.dll 2007.0100.1600.022 ((SQL_PreRelease).080709-1414 )
C:\Windows\syswow64\CFGMGR32.dll 6.1.7601.17621 (win7sp1_gdr.110523-2108)
C:\Windows\system32\rasman.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\System32\perfos.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.5420 (Win7SP1.050727-5400)

Application:       BotTemplate
Version:           *******
Date:              20.04.2014 12:29:54
Computer name:     ANO-PC
User name:         Ano
OS:                Microsoft Windows NT 6.1.7601 Service Pack 1
Culture:           de-DE
Resolution:        {Width=1280, Height=960}
System up time:    03:40:04.4910000
App up time:       00:04:47.2534299
Total memory:      7159Mb
Available memory:  3525Mb

Exception classes:   
   System.Exception
Exception messages: 
   ReadByte failed.
Stack Traces:
   bei Magic.SMemory.ReadByte(IntPtr hProcess, UInt32 dwAddress) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Helper\BlackMagic\Static Classes\SMemory.cs:Zeile 136.
   bei Magic.BlackMagic.ReadByte(UInt32 dwAddress) in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Helper\BlackMagic\BMMemory.cs:Zeile 211.
   bei BotTemplate.Interact.Calls.IsLooting() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Interact\Calls.cs:Zeile 306.
   bei BotTemplate.Engines.Stockades.States.stateStockInside.Run() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Engines\ZulFarrak\States\stateStockInside.cs:Zeile 74.
   bei BotTemplate.Engines.Engine.Pulse() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Engines\Engine.cs:Zeile 28.
   bei BotTemplate.Engines.Engine.Run() in c:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\Engines\Engine.cs:Zeile 57.
   bei System.Threading.ThreadHelper.ThreadStart_Context(Object state)
   bei System.Threading.ExecutionContext.Run(ExecutionContext executionContext, ContextCallback callback, Object state)
   bei System.Threading.ThreadHelper.ThreadStart()
Loaded Modules:
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\BotTemplate.exe *******
C:\Windows\SysWOW64\ntdll.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\SYSTEM32\MSCOREE.DLL 4.0.40305.0 (Main.040305-0000)
C:\Windows\syswow64\KERNEL32.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\KERNELBASE.dll 6.1.7601.18015 (win7sp1_gdr.121129-1432)
C:\Windows\syswow64\ADVAPI32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\msvcrt.dll 7.0.7601.17744 (win7sp1_gdr.111215-1535)
C:\Windows\SysWOW64\sechost.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\RPCRT4.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\SspiCli.dll 6.1.7601.18270 (win7sp1_gdr.130924-1532)
C:\Windows\syswow64\CRYPTBASE.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\mscoreei.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\syswow64\SHLWAPI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\GDI32.dll 6.1.7601.18275 (win7sp1_gdr.131002-1533)
C:\Windows\syswow64\USER32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\LPK.dll 6.1.7601.18177 (win7sp1_gdr.130605-1534)
C:\Windows\syswow64\USP10.dll 1.0626.7601.18009 (win7sp1_gdr.121121-1431)
C:\Windows\system32\IMM32.DLL 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\MSCTF.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorwks.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\WinSxS\x86_microsoft.vc80.crt_1fc8b3b9a1e18e3b_8.0.50727.6229_none_d089f796442de10e\MSVCR80.dll 8.00.50727.6229
C:\Windows\syswow64\shell32.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\syswow64\ole32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\profapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\NativeImages_v2.0.50727_32\mscorlib\ede2c6c842840e009f01bcc74fa4c457\mscorlib.ni.dll 2.0.50727.5477 (Win7SP1GDR.050727-5400)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorjit.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System\b3a78269847005365001c33870cd121f\System.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Drawing\5c24d3b0041ebf4f48a93615b9fa3de9\System.Drawing.ni.dll 2.0.50727.5467 (Win7SP1GDR.050727-5400)
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Windows.Forms\8bc548587e91ecf0552a40e47bbf99cc\System.Windows.Forms.ni.dll 2.0.50727.5468 (Win7SP1GDR.050727-5400)
C:\Windows\system32\CRYPTSP.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\rsaenh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\RpcRtRemote.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Users\<USER>\Documents\Visual Studio 2013\Projects\BotTemplate\BotTemplate\bin\Release\fasmdll_managed.dll 
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\MSVCR90.dll 9.00.30729.6161
C:\Windows\WinSxS\x86_microsoft.vc90.crt_1fc8b3b9a1e18e3b_9.0.30729.6161_none_50934f2ebcb7eb57\msvcm90.dll 9.00.30729.6161
C:\Windows\assembly\GAC_MSIL\mscorlib.resources\2.0.0.0_de_b77a5c561934e089\mscorlib.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\system32\uxtheme.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\assembly\GAC_MSIL\System.resources\2.0.0.0_de_b77a5c561934e089\System.resources.dll 2.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\WinSxS\x86_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.7601.18120_none_72d2e82386681b36\gdiplus.dll 6.1.7601.18120 (win7sp1_gdr.130402-1532)
C:\Windows\WinSxS\x86_microsoft.windows.common-controls_6595b64144ccf1df_6.0.7601.17514_none_41e6975e2bd6f2b2\comctl32.dll 6.10 (win7_rtm.090713-1255)
C:\Windows\syswow64\OLEAUT32.DLL 6.1.7601.17676
C:\Windows\assembly\NativeImages_v2.0.50727_32\System.Core\868ad9d8acc0bf80a973c0e4e9cae4fa\System.Core.ni.dll 3.5.30729.5420 built by: Win7SP1
C:\Windows\syswow64\psapi.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\VERSION.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\ntmarta.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\WLDAP32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\pcwum.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\apphelp.dll 6.1.7601.17514 (win7sp1_rtm.101119-1850)
C:\Windows\Microsoft.NET\Framework\v4.0.30319\perfcounter.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\system32\pdh.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\system32\MSVCR110_CLR0400.dll 11.00.50938.18408 built by: FX451RTMGREL
C:\Windows\system32\aspnet_counters.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v4.0.30319\aspnet_perf.dll 4.0.30319.18408 built by: FX451RTMGREL
C:\Windows\Microsoft.NET\Framework\v1.1.4322\aspnet_isapi.dll 1.1.4322.2502
C:\Windows\system32\USERENV.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v1.1.4322\MSVCR71.dll 7.10.3052.4
C:\Windows\syswow64\WS2_32.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\syswow64\NSI.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Program Files (x86)\Microsoft SQL Server\100\Shared\instapi10.dll 2007.0100.1600.022 ((SQL_PreRelease).080709-1414 )
C:\Windows\syswow64\CFGMGR32.dll 6.1.7601.17621 (win7sp1_gdr.110523-2108)
C:\Windows\system32\rasman.dll 6.1.7600.16385 (win7_rtm.090713-1255)
C:\Windows\Microsoft.NET\Framework\v2.0.50727\diasymreader.dll 8.0.50727.5420 (Win7SP1.050727-5400)
C:\Windows\System32\perfos.dll 6.1.7600.16385 (win7_rtm.090713-1255)
